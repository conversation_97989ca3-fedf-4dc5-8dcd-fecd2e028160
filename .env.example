# AI Model Configuration
MAGONIA_AI_MODEL="gemini"  # Options: gemini, gpt4o, mistral (leave empty for auto-selection)

# API Keys
OPENAI_API_KEY="chatgpt_key"
GEMINI_API_KEY="your-gemini-api-key-here"
MISTRAL_API_KEY="your-mistral-api-key-here"
TAVILY_API_KEY="tvly-"
LANGCHAIN_API_KEY=lsv2_pt_
LANGCHAIN_TRACING_V2=True
LANGCHAIN_PROJECT="ReAct LangGraph"
DEBUG=True
PORT=80
SEABEX_API_TOKEN="..-tXsXjlRVVADiPwev4mC77h_pTu75BWg6R--IGBCMlQZhOHTm08lAgBGIiMaxUXAlxoA-Up9RvKC6x9v90lRYFMKKNezAQWSfaj5RSDz0gBW1AOTsH6DEkvgyRdrv1RzJUFfGn0vXtjogoH6VA237NfcHCtvUH7_BDywUxhpmPDKrsnFC40zdGA75dRQm-NZCC3iwSZadsDyFHEbNlPvE1AgEeUPI"
REDIS_URL='redis://0.0.0.0:6379'
AUTH_TOKEN="test"