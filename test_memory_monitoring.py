#!/usr/bin/env python3
"""
Test script to verify memory monitoring functionality in GeminiDirectTools.
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_memory_monitoring():
    """Test the memory monitoring functionality."""
    print("🧪 Testing Memory Monitoring in GeminiDirectTools")
    print("=" * 60)
    
    try:
        # Import the GeminiDirectTools class
        from magonia.gemini_direct import GeminiDirectTools
        
        print("✅ Successfully imported GeminiDirectTools")
        
        # Initialize the tools (this should log initial memory usage)
        print("\n📊 Initializing GeminiDirectTools...")
        gemini_tools = GeminiDirectTools(
            enable_translation=False,  # Disable for faster testing
            enable_context_analysis=False
        )
        
        print("✅ GeminiDirectTools initialized successfully")
        
        # Test memory status method
        print("\n📊 Getting memory status...")
        memory_status = gemini_tools.get_memory_status()
        print(f"Memory Status: {memory_status}")
        
        # Test a simple question (this should log memory usage throughout)
        print("\n📊 Testing with a simple question...")
        test_question = "Hello, how are you?"
        
        print(f"Question: {test_question}")
        response = gemini_tools.ask(test_question, user_id="test_user")
        print(f"Response: {response}")
        
        print("\n✅ Memory monitoring test completed successfully!")
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Make sure all dependencies are installed:")
        print("  pip install psutil google-generativeai")
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()

def test_memory_status_only():
    """Test only the memory status functionality without API calls."""
    print("🧪 Testing Memory Status Only")
    print("=" * 40)
    
    try:
        from magonia.gemini_direct import GeminiDirectTools
        
        # Initialize with minimal configuration
        gemini_tools = GeminiDirectTools()
        
        # Get memory status multiple times to see changes
        for i in range(3):
            print(f"\n📊 Memory check #{i+1}:")
            memory_status = gemini_tools.get_memory_status()
            
            if memory_status.get("available"):
                print(f"  Total RAM: {memory_status['total_gb']} GB")
                print(f"  Used RAM: {memory_status['used_gb']} GB ({memory_status['percent_used']}%)")
                print(f"  Available RAM: {memory_status['available_gb']} GB")
                print(f"  Process Memory: {memory_status['process_memory_mb']} MB ({memory_status['process_memory_percent']}%)")
            else:
                print(f"  Memory monitoring not available: {memory_status}")
                
            # Create some memory usage to see changes
            if i < 2:
                dummy_data = [0] * 1000000  # Create a list with 1M integers
                print(f"  Created dummy data with {len(dummy_data)} elements")
                del dummy_data  # Clean up
        
        print("\n✅ Memory status test completed!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("Choose test mode:")
    print("1. Full test (with API calls)")
    print("2. Memory status only (no API calls)")
    
    choice = input("Enter choice (1 or 2): ").strip()
    
    if choice == "1":
        test_memory_monitoring()
    elif choice == "2":
        test_memory_status_only()
    else:
        print("Invalid choice. Running memory status test only...")
        test_memory_status_only()
