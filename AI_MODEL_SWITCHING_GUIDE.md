# AI Model Switching Guide

This guide explains how to easily switch between different AI models (Gemini, GPT-4o, Mistral) in your Magonia system.

## ✅ What's Been Implemented

### 🔧 **Complete Tool Parity**
- **Gemini**: 31 tools (same as GPT-4o)
- **GPT-4o**: 31 tools
- All irrigation, time, memory, and document lookup tools are available in both models

### 🚀 **Easy Model Switching**
Three ways to switch between AI models:

1. **Environment Variable** (Recommended)
2. **Code-based Switching**
3. **Automatic Priority System**

### 🛡️ **Robust Fallback System**
- If primary model fails → automatically tries fallback models
- Graceful error handling with user-friendly messages
- No service interruption

## 🎯 How to Switch Models

### Method 1: Environment Variable (Recommended)

Set the `MAGONIA_AI_MODEL` environment variable:

```bash
# Use Gemini as primary
export MAGONIA_AI_MODEL=gemini

# Use GPT-4o as primary  
export MAGONIA_AI_MODEL=gpt4o

# Use Mistral as primary
export MAGONIA_AI_MODEL=mistral

# Auto-select (uses priority: Gemini → GPT-4o → Mistral)
export MAGONIA_AI_MODEL=""
```

**In .env file:**
```
MAGONIA_AI_MODEL=gemini
```

### Method 2: Code-based Switching

```python
from magonia.magonia_ai import Magonia

magonia = Magonia()

# Switch to Gemini
magonia.switch_ai_model("gemini")

# Switch to GPT-4o
magonia.switch_ai_model("gpt4o")
# or
magonia.switch_ai_model("chatgpt")  # alias

# Switch to Mistral
magonia.switch_ai_model("mistral")

# Check current status
magonia.get_ai_status()
```

### Method 3: Configuration API

```python
from magonia.ai_config import switch_to_gemini, switch_to_gpt4o, print_model_status

# Switch models
switch_to_gemini()
switch_to_gpt4o()

# Check status
print_model_status()
```

## 📋 Model Status Check

To see which models are available and which is currently primary:

```python
from magonia.ai_config import print_model_status

print_model_status()
```

Output example:
```
🤖 AI Model Status:
========================================
GEMINI: ✅ Available (PRIMARY)
  API Key: GEMINI_API_KEY

GPT4O: ✅ Available
  API Key: OPENAI_API_KEY

MISTRAL: ❌ Not Available
  API Key: MISTRAL_API_KEY
  Issue: Missing or invalid API key in MISTRAL_API_KEY

🎯 Current Primary Model: GEMINI
```

## 🔑 API Key Configuration

Make sure you have the required API keys in your `.env` file:

```bash
# Primary model (Gemini)
GEMINI_API_KEY=your-actual-gemini-api-key

# Fallback model (GPT-4o)
OPENAI_API_KEY=your-actual-openai-api-key

# Optional (Mistral)
MISTRAL_API_KEY=your-actual-mistral-api-key

# Model selection (optional)
MAGONIA_AI_MODEL=gemini
```

## 🔄 Automatic Fallback System

The system automatically handles failures:

1. **Primary Model Fails** → Tries next available model
2. **All Models Fail** → Returns user-friendly error message
3. **No Models Available** → Shows configuration help

Example flow:
```
User Question → Gemini (primary) → Success ✅
User Question → Gemini (primary) → Fails → GPT-4o (fallback) → Success ✅
User Question → Gemini (primary) → Fails → GPT-4o (fallback) → Fails → Error message
```

## 🧪 Testing Your Setup

Run the comprehensive test to verify everything works:

```bash
python test_complete_gemini_tools.py
```

This test verifies:
- ✅ All 31 tools work in Gemini
- ✅ Model switching functionality
- ✅ Both models handle irrigation questions
- ✅ Fallback system works
- ✅ Configuration system works

## 💡 Best Practices

### For Development
```bash
# Use Gemini for cost efficiency
export MAGONIA_AI_MODEL=gemini
```

### For Production
```bash
# Use auto-selection for reliability
export MAGONIA_AI_MODEL=""
# This will use Gemini if available, fallback to GPT-4o
```

### For Testing
```python
# Test both models
magonia.switch_ai_model("gemini")
response1 = magonia.ask("", "What time is it?")

magonia.switch_ai_model("gpt4o") 
response2 = magonia.ask("", "What time is it?")
```

## 🔍 Troubleshooting

### Model Not Available
```
❌ Cannot switch to GEMINI - model not available (check API key)
```
**Solution**: Check your `GEMINI_API_KEY` in the `.env` file

### No Models Available
```
⚠️ No AI models are currently available! Please configure API keys.
```
**Solution**: Add at least one valid API key (`GEMINI_API_KEY` or `OPENAI_API_KEY`)

### Tool Execution Errors
```
Error retrieving irrigation data: Working outside of application context
```
**Solution**: This is expected in test environments. In production (Flask app), this won't occur.

## 📊 Model Comparison

| Feature | Gemini | GPT-4o | Mistral |
|---------|--------|--------|---------|
| **Tools** | 31 ✅ | 31 ✅ | 31 ✅ |
| **Cost** | Lower | Higher | Medium |
| **Speed** | Fast | Fast | Fast |
| **Multilingual** | Excellent | Good | Good |
| **Reliability** | High | High | High |

## 🚀 Quick Start Commands

```bash
# 1. Set up Gemini as primary
echo "MAGONIA_AI_MODEL=gemini" >> .env
echo "GEMINI_API_KEY=your-key-here" >> .env

# 2. Test the setup
python test_complete_gemini_tools.py

# 3. Use in your code
python -c "
from magonia.magonia_ai import Magonia
m = Magonia()
print(m.ask('', 'What time is it?'))
"
```

## 🎉 Summary

You now have:
- ✅ **Complete tool parity** between Gemini and GPT-4o (31 tools each)
- ✅ **Easy model switching** via environment variables or code
- ✅ **Automatic fallback system** for reliability
- ✅ **Cost optimization** by using Gemini as primary
- ✅ **Production-ready** configuration system

The system will use Gemini by default (cost-efficient) with GPT-4o as a reliable fallback, giving you the best of both worlds! 🌟
