"""
AI Model Configuration for Magonia
This module provides easy switching between different AI models (Gemini, GPT-4o, etc.)
"""

import os
from enum import Enum
from typing import Optional, Dict, Any

class AIModel(Enum):
    """Supported AI models"""
    GEMINI = "gemini"
    GPT4O = "gpt4o"
    MISTRAL = "mistral"

class AIConfig:
    """Configuration class for AI model selection and settings"""
    
    def __init__(self):
        # Default model priority (first available will be used)
        self.model_priority = [AIModel.GEMINI, AIModel.GPT4O, AIModel.MISTRAL]
        
        # Model-specific settings
        self.model_settings = {
            AIModel.GEMINI: {
                "api_key_env": "GEMINI_API_KEY",
                "model_name": "gemini-2.5-flash-lite",
                "temperature": 0.7,
                "max_tokens": 2000,
                "enable_translation": True,
                "enable_context_analysis": True
            },
            AIModel.GPT4O: {
                "api_key_env": "OPENAI_API_KEY",
                "model_name": "gpt-4o",
                "temperature": 0.7,
                "max_tokens": 2000,
                "enable_translation": True,
                "enable_context_analysis": True
            },
            AIModel.MISTRAL: {
                "api_key_env": "MISTRAL_API_KEY",
                "model_name": "mistral-large",
                "temperature": 0.7,
                "max_tokens": 2000,
                "enable_translation": True,
                "enable_context_analysis": True
            }
        }
    
    def get_primary_model(self) -> Optional[AIModel]:
        """Get the primary AI model based on environment variable or default priority"""
        # Check if a specific model is forced via environment variable
        forced_model = os.getenv("MAGONIA_AI_MODEL", "").lower()
        if forced_model:
            for model in AIModel:
                if model.value == forced_model:
                    if self.is_model_available(model):
                        return model
                    else:
                        print(f"Warning: Forced model {forced_model} is not available (missing API key)")
        
        # Use priority order to find first available model
        for model in self.model_priority:
            if self.is_model_available(model):
                return model
        
        return None
    
    def is_model_available(self, model: AIModel) -> bool:
        """Check if a model is available (has API key configured)"""
        settings = self.model_settings.get(model)
        if not settings:
            return False
        
        api_key_env = settings["api_key_env"]
        api_key = os.getenv(api_key_env)
        
        return api_key is not None and api_key.strip() != "" and api_key != f"your-{model.value}-api-key-here"
    
    def get_model_settings(self, model: AIModel) -> Dict[str, Any]:
        """Get settings for a specific model"""
        return self.model_settings.get(model, {})
    
    def get_available_models(self) -> list[AIModel]:
        """Get list of all available models"""
        return [model for model in AIModel if self.is_model_available(model)]
    
    def set_model_priority(self, priority: list[AIModel]):
        """Set custom model priority order"""
        self.model_priority = priority
    
    def force_model(self, model: AIModel) -> bool:
        """Force use of a specific model (returns True if successful)"""
        if self.is_model_available(model):
            self.model_priority = [model] + [m for m in self.model_priority if m != model]
            return True
        return False

# Global configuration instance
ai_config = AIConfig()

def get_ai_config() -> AIConfig:
    """Get the global AI configuration instance"""
    return ai_config

def set_primary_model(model: AIModel) -> bool:
    """Set the primary AI model"""
    return ai_config.force_model(model)

def get_primary_model() -> Optional[AIModel]:
    """Get the current primary AI model"""
    return ai_config.get_primary_model()

def is_model_available(model: AIModel) -> bool:
    """Check if a specific model is available"""
    return ai_config.is_model_available(model)

def get_available_models() -> list[AIModel]:
    """Get list of available AI models"""
    return ai_config.get_available_models()

def print_model_status():
    """Print status of all AI models"""
    print("🤖 AI Model Status:")
    print("=" * 40)
    
    primary = get_primary_model()
    available = get_available_models()
    
    for model in AIModel:
        status = "✅ Available" if model in available else "❌ Not Available"
        primary_marker = " (PRIMARY)" if model == primary else ""
        settings = ai_config.get_model_settings(model)
        api_key_env = settings.get("api_key_env", "")
        
        print(f"{model.value.upper()}: {status}{primary_marker}")
        print(f"  API Key: {api_key_env}")
        
        if model not in available:
            print(f"  Issue: Missing or invalid API key in {api_key_env}")
        
        print()
    
    if primary:
        print(f"🎯 Current Primary Model: {primary.value.upper()}")
    else:
        print("⚠️  No AI models are available! Please configure API keys.")

# Example usage functions
def switch_to_gemini() -> bool:
    """Switch to Gemini as primary model"""
    return set_primary_model(AIModel.GEMINI)

def switch_to_gpt4o() -> bool:
    """Switch to GPT-4o as primary model"""
    return set_primary_model(AIModel.GPT4O)

def switch_to_mistral() -> bool:
    """Switch to Mistral as primary model"""
    return set_primary_model(AIModel.MISTRAL)

if __name__ == "__main__":
    # Print current status when run directly
    print_model_status()
