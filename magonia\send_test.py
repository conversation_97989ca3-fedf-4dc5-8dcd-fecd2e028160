import os
import random
import string
import time
import traceback
from flask import json
import requests
import json
from dotenv import load_dotenv
load_dotenv()

token = os.getenv('AUTH_TOKEN')


def send_prompt(prompt, session_id="session123", user_id="f68381cd-a748-47bd-842c-701790b35e3c", token=None):
    url = "http://127.0.0.1:8080/magonia/chat"
    data = {
        "session_id": session_id,
        "prompt": prompt,
        "chat_history": "",
        "scopes": ["magonia-api"],
        "user_id": user_id
    }
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {token}"
    }
    response = requests.post(url, headers=headers, data=json.dumps(data))
    print("Status Code:", response.status_code)

    if response.status_code != 200:
        print("Response Text:", response.text)
        try:
            return response.text
        except Exception as e:
            print(e)
            traceback.print_exc()
            return {
                'error': 'An unexpected error occurred, and the response is not valid JSON.',
                'status_code': response.status_code
            }

    try:
        json_response = response.json()
        print("\033[33m" + "Response: " + json_response + "\033[0m")
        return json_response
    except Exception as e:
        print(e)
        traceback.print_exc()
        return None



# Prompts to send
prompts = [
    "Hi! my name is kalil",
    #"Do I have to irrigate my field CHlewi on 2024-02-21",
    #"get_all_user_areas_with_children",
    #"Do I have to irrigate my field",
    #"Do I have to irrigate my field on a date that I did miss",
    "What is my name",
    #"Do I have to irrigate my field today",
    #"Comment remplir les informations de la parcelle?",
    #"What is the date of today",
    #"Hi! my name is Dali",
    #"what s the date of today?",
    #"?كيفاش نضمن توزيع متساوي للماء في نظام الري",
    #"what s my name again ?",
    #"يلزمني نسقي الحقل متاعي ش ChleWi لوي نهار 2024-01-21؟ ",
    #"what s the better way to irragte ?",
    #"what s my name again ?",
    #"Comment tracer ma parcelle?",
    #"Seabex peut-il m'aider à gérer l'irrigation de plusieurs champs ou parcelles ?",
    #"How many fields have an active irrigation recommendation for today?",
    #"Do I need to irrigate my field ChleWi during the period from 2024-7-10 to 2024-10-22?",
    #"What was the soil water volume for the field ChleWi on 2024-10-23?",
    #"Which field has the earliest next recommended irrigation date?",
    #"what s the time now ?"
    #"Which field had the highest real evapotranspiration value on 2024-10-23?",
    #"Which fields need irrigation tomorrow?",
    #"Which field has the highest irrigation requirement over the next week?",
    #"When should I stop irrigating my field chlewi to avoid over-irrigation?",
    #"Which fields have the lowest soil water volume today?",
    #"Which fields are expected to have optimal soil moisture for the next 7 days?",
    #"What is the total irrigation volume recommended for all fields in the next 2 days?",
    #"which field will have the earliest irrigation requirement after today?",
    #"Which fields are predicted to have no irrigation needs in the next 6 days?",
    #"What is the predicted water consumption rate for each field tomorrow?",
    #"Which fields will require the most water over the next 5 days?",
    #"Are there any fields predicted to have optimal soil moisture levels in the next 5 days?",
    #"Which fields will exceed their water capacity by the end of this week?",
    #"What is the current irrigation status of all fields for today?",
    #"Are any fields expected to exceed their soil water capacity tomorrow?",
    #"What is the average soil water volume for all fields today?",
    #"Which fields are expected to have optimal soil moisture for the next 7 days?",
    #"What is the total water consumption predicted for each field over the next 2 days?",
    #"Can you list all my fields?",
    #"hello ,?كيفاش نضمن توزيع متساوي للماء في نظام الري",
    #"ahla labas 3Lik ?",
    #"what is the best time to irrigate my fields ?",
    #"Quels sont les avantages de l'irrigation goutte-à-goutte pour la gestion de l'eau en agriculture ?"
    #"hello bonjour ça va radiation par semaine",
    #"comment on preparer une pizza ?",
    #"qu est ce que je dois irriger aujourdhui ?"
    #"do i have to irrigate my field Today ?"
    #"what are all the field's names of mine ",
    #"obtenir toute ma zone ?",
    #"est ce que je dois irriguer aujourdhui ?",
    #"combien je dois apporter ?",
    #"combien je dois irriguer aujourdhui ?",
    #"quelles sont mes parcelles ?",
    #"get_all_user_areas_with_children",
    #"What are my fields ?",
    #"A ce que je dois irriguer ajourd'hui?",
    #"yes , i want to know more ? ",
    #"what are your tools list ?"
    #'what s the date of today ?',
    #'¿Tengo que regar mi campo CHlewi el 21 de enero de 2024?',
    #"what are the best places to visit them arround the world ?",
    #"what s my name ?"
    #"do i have to irrigate my field Taba1 on 2024-05-20 ?"
    #"who is magonia ?",
    #"est ce que je dois irriguer aujourd hui ? ",
    #"Quelles sont les céréales supportées par seabex?",
    #"Comment tracer ma parcelle?",
    #"Comment créer un cycle de production et activer le calcul du bilan hydrique?",
    #"Comment remplir les information de la parcelle?",
    #"Quelles sont les cultures supportées par la plateforme Seabex?",
    #"A ce que je dois irriguer aujourd'hui ?",
    #"يجب أن أسقي اليوم؟",
    #"هل يجب علي أن أقوم بالري اليوم؟",
    #"Comment calculer les doses d'engrais à apporter ?",
    #"شنوة أنواع نخيل التمر اللي يدعموهم Seabex؟"
    #"ما هي الحقول التي من المتوقع أن تتمتع برطوبة التربة المثالية خلال الأيام السبعة المقبلة؟",
    #"ما هو إجمالي استهلاك المياه المتوقع لكل حقل خلال اليومين القادمين؟",
    #"Quelle est la consommation totale d'eau prévue pour chaque champ au cours des deux prochains jours ?",
    #"a ce que je dois irriguer aujourd'hui ?"
    #"do i need to irrigate in the next days ?",
    #"do i need to stop irrigate my field CHlewi ?"
    #"get_lowest_soil_water_volume",
    #"find fields no irrigation needs for 2 days"
    #"a ce que je dois irriguer aujourdhui ?",
    #"combiend je dois irriguer chlewi ?",
    #"do i have to irrigate sidi salah1 today ?"
    #"do i have field to irrigate for tomorrow ?"
    #"affiche moi le nom du champ qui a besoin de plus d irrigation ?"



]

# Start the timer
start_time = time.time()
session_id = "session123"
user_id = "f68381cd-a748-47bd-842c-701790b35e3c"
print(f"Using session_id: {session_id}")
print(f"Using user_id: {user_id}")

# Your prompt processing loop
for prompt in prompts:
    start_time = time.time()
    print("\033[31m" + "PROMPT : " + "\033[0m" + "\033[32m" + prompt + "\033[0m")
    send_prompt(prompt=prompt, session_id=session_id, user_id=user_id, token=token)
    end_time = time.time()
    execution_time = end_time - start_time
    print(f"Execution time: {execution_time:.2f} seconds")

# End the timer
end_time = time.time()


execution_time = end_time - start_time


if execution_time < 1:
    execution_message = f"{execution_time * 1000:.2f} ms"
elif execution_time < 60:
    execution_message = f"{execution_time:.2f} seconds"
else:
    minutes = execution_time // 60
    seconds = execution_time % 60
    execution_message = f"{int(minutes)} minute(s) and {seconds:.2f} seconds"

print(f"Execution time: {execution_message}")


# print("new Session : session555")
# send_prompt(prompt="What is my name", session_id="session555", token=token, )
